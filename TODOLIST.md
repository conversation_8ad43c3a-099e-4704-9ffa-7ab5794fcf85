# 餐厅数据爬取系统 - 核心任务清单

## 🎯 当前项目状态
- ✅ 多国家餐厅数据爬取系统已完成
- ✅ 5步数据处理流程已建立
- ✅ README.md 文档已更新
- ✅ 核心痛点已明确：需要将5步命令行操作web化

## 🚀 核心目标
**将README.md中的5步数据处理流程实现可视化操作串联，用户通过web界面一键执行整个流程**

---

## 📋 重新规划的任务清单

### 🔧 第一阶段：流程自动化优化 (1-2周)
**目标**：将5个手动步骤整合成自动化pipeline

- [ ] **创建统一Pipeline脚本 (main_pipeline.py)**
  - [ ] 串联5个步骤：scrape_vietnam.py → URL格式化 → scrape_restaurant_urls.py → restaurant.py → translator.py
  - [ ] 实现步骤间依赖检查（检查前置文件是否存在且有效）
  - [ ] 添加国家参数统一传递机制
  - [ ] 实现简单的控制台进度输出

- [ ] **自动化URL格式化处理**
  - [ ] 将parse_city_urls.py的逻辑集成到pipeline中
  - [ ] 自动检测和处理URL格式问题
  - [ ] 添加URL有效性验证

- [ ] **智能错误处理和恢复**
  - [ ] 每个步骤添加异常捕获和详细日志
  - [ ] 实现失败步骤的自动重试机制（最多3次）
  - [ ] 支持从失败步骤继续执行（真正的断点续传）
  - [ ] 生成用户友好的错误报告

- [ ] **并发处理优化**
  - [ ] 支持多国家并发处理（解决你提到的痛点）
  - [ ] 确保多进程间数据不重复（文件锁机制）
  - [ ] 优化三个浏览器实例的资源分配

### 🌐 第二阶段：极简Web化 (2-3周)
**目标**：创建最简单的web界面，实现一键执行5步流程

- [ ] **极简Web界面设计**
  - [ ] 单页面HTML界面（无需复杂框架）
  - [ ] 国家多选框：印度尼西亚、菲律宾、马来西亚
  - [ ] 大大的"开始处理"按钮
  - [ ] 5步进度条，清晰显示当前步骤
  - [ ] 简单的状态提示：等待中/进行中/已完成/出错

- [ ] **轻量级后端实现**
  - [ ] Flask应用（比FastAPI更简单）
  - [ ] 核心API接口：
    - POST /start：启动处理流程
    - GET /status：获取当前处理状态
    - GET /files：获取生成的文件列表
    - GET /download/{file}：下载文件
  - [ ] WebSocket实现实时进度推送

- [ ] **核心功能实现**
  - [ ] 后端调用pipeline脚本
  - [ ] 实时进度更新推送到前端
  - [ ] 处理完成后自动显示下载链接
  - [ ] 基础的错误处理和用户友好提示

### 🎨 第三阶段：用户体验优化 (1-2周)
**目标**：让界面更友好，操作更便利

- [ ] **界面美化**
  - [ ] 使用简单的CSS框架（如Bootstrap）
  - [ ] 添加合适的图标和颜色
  - [ ] 进度条添加动画效果
  - [ ] 响应式设计（手机也能用）

- [ ] **功能增强**
  - [ ] 添加"暂停处理"和"继续处理"功能
  - [ ] 显示预计剩余时间
  - [ ] 处理历史记录（最近10次）
  - [ ] 支持重新处理特定国家

- [ ] **错误处理优化**
  - [ ] 更友好的错误提示信息
  - [ ] 提供错误恢复建议
  - [ ] 简单的日志查看功能
  - [ ] 一键重试失败的步骤

### 🚀 第四阶段：实用扩展 (可选，1-2周)
**目标**：根据实际使用体验添加实用功能

- [ ] **简单配置管理**
  - [ ] Chrome浏览器路径配置
  - [ ] 爬虫延时参数调整
  - [ ] Google Gemini API密钥配置
  - [ ] 并发数量设置

- [ ] **数据管理功能**
  - [ ] 文件预览（显示CSV前10行）
  - [ ] 批量文件下载（ZIP打包）
  - [ ] 简单的数据统计（餐厅数量、城市数量）
  - [ ] 历史数据清理功能

- [ ] **系统扩展**
  - [ ] 添加新国家的简单界面
  - [ ] 自定义处理步骤选择（可以从第3步开始）
  - [ ] 定时任务功能（每天自动更新）

---

## 🎯 立即行动计划 (本周重点)

### 🔥 最高优先级：流程自动化
1. **创建main_pipeline.py** - 串联5个步骤的核心脚本
2. **解决并发数据重复问题** - 你提到的核心痛点
3. **测试完整流程** - 确保一键执行可行

### 📋 具体行动步骤：
```bash
# 第1天：创建pipeline脚本框架
# 第2-3天：集成5个步骤，添加依赖检查
# 第4-5天：实现并发处理和数据去重
# 第6-7天：测试和优化
```

## 📊 项目里程碑（重新规划）

### 🎯 里程碑1：Pipeline自动化完成 (2周内)
- [ ] 5步流程完全自动化
- [ ] 并发处理问题解决
- [ ] 一键执行整个流程

### 🌐 里程碑2：基础Web界面完成 (4周内)
- [ ] 简单的web界面
- [ ] 一键启动功能
- [ ] 实时进度显示

### ✨ 里程碑3：用户体验优化完成 (6周内)
- [ ] 界面美化
- [ ] 错误处理优化
- [ ] 基础配置功能

### 🚀 里程碑4：实用扩展完成 (8周内，可选)
- [ ] 高级配置
- [ ] 数据管理
- [ ] 系统扩展

## 💡 核心设计理念

### ✅ 要做的：
- **极简设计**：界面简单，操作直观
- **一键操作**：选择国家 → 点击开始 → 等待完成
- **实时反馈**：清楚知道当前在做什么
- **错误友好**：出错时知道怎么解决

### ❌ 不做的：
- 复杂的任务管理系统
- 复杂的数据可视化图表
- 用户权限管理
- 复杂的监控告警

## 🚨 关键风险控制

### 技术风险：
- **并发数据重复**：使用文件锁确保数据一致性
- **步骤依赖失败**：完善的错误检查和恢复机制
- **资源占用过高**：合理控制并发数量

### 时间风险：
- **功能蔓延**：严格按照MVP原则，先实现核心功能
- **技术选型**：选择熟悉的技术栈，避免学习成本

## 📝 成功标准

### 第一阶段成功标准：
- [ ] 用户运行一个命令，自动完成5个步骤
- [ ] 支持多国家并发处理
- [ ] 数据不重复，错误能恢复

### 第二阶段成功标准：
- [ ] 用户打开网页，选择国家，点击开始
- [ ] 能看到实时进度，知道当前在做什么
- [ ] 完成后能下载结果文件

---

*重新规划后的TODO列表聚焦在你的核心需求：将5步命令行操作web化，实现一键执行*
