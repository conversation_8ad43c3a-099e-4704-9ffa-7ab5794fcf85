# 越南餐厅数据爬取项目

本项目旨在从TripAdvisor网站爬取越南各城市的餐厅信息，包括餐厅名称、评分、价格、品类、评论数等详细信息。

## 项目结构

```
city_restaurants/
├── output/                          # 输出文件夹
│   ├── scrape_city_urls.json        # 城市URL列表
│   └── scrape_restaurant_urls.json  # 餐厅URL数量统计
├── __init__.py                      # Python包初始化文件
├── restaurant.py                    # 餐厅详情爬虫
├── scrape_restaurant_urls.py        # 餐厅URL爬虫
├── scrape_vietnam.py                # 越南城市URL爬虫
├── vietnam_city_urls.csv            # 越南城市URL列表
├── vietnam_restaurant_urls.csv      # 越南餐厅URL列表
└── pending_pages.csv                # 待处理页面列表
```

## 功能模块

### 1. scrape_vietnam.py - 城市列表爬虫
爬取越南各城市的餐厅列表页面URL，保存到 `vietnam_city_urls.csv` 文件中。

### 2. scrape_restaurant_urls.py - 餐厅URL爬虫
根据城市列表中的URL，逐个访问并提取各城市的所有餐厅详情页面URL，保存到 `vietnam_restaurant_urls.csv` 文件中。

### 3. restaurant.py - 餐厅详情爬虫
根据已获取的餐厅URL列表，爬取每个餐厅的详细信息，包括：
- 品牌名称
- 评分
- 平均价格
- 一级品类和二级品类
- 评论数目
- 电话和邮箱
- 品牌链接
- 地址

数据将保存到 `restaurant_details.csv` 文件中。

## 使用方法

1. 确保已安装所需依赖：
   ```
   pip install botasaurus
   ```

2. 运行城市列表爬虫：
   ```
   python scrape_vietnam.py
   ```

3. 运行餐厅URL爬虫：
   ```
   python scrape_restaurant_urls.py
   ```

4. 运行餐厅详情爬虫：
   ```
   python restaurant.py
   ```

## 注意事项
- 遇到报错尝试清空cache
- 项目使用了botasaurus框架进行网页爬取
- 为了防止被反爬，代码中加入了随机延时和模拟用户行为
- 爬虫支持断点续传，可以在中断后继续执行
- 请遵守网站的robots.txt规则和使用条款，合理控制爬取频率